from rest_framework.generics import CreateAPI<PERSON>iew, ListAPIView
from rest_framework.response import Response
from rest_framework import status
from tenants.models import PendingClient, Client
from .serializers import PendingClientSerializer
from restaurant.api.serializers import RestaurantSerializer
from rest_framework.permissions import AllowAny

class RegisterRestaurantView(CreateAPIView):
    queryset = PendingClient.objects.all()
    serializer_class = PendingClientSerializer
    permission_classes = [AllowAny]

class FeaturedRestaurantsView(ListAPIView):
    """
    Public endpoint to fetch featured/active restaurants for the landing page
    """
    serializer_class = RestaurantSerializer
    permission_classes = [AllowAny]

    def get_queryset(self):
        # Return active restaurants, limit to featured ones
        return Client.objects.filter(active=True).order_by('-created_on')[:6]

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        # Transform data to match frontend expectations
        featured_restaurants = []
        for restaurant in serializer.data:
            featured_restaurants.append({
                'id': restaurant.get('id'),
                'name': restaurant.get('restaurant_name'),
                'cuisine': 'Malawian',  # Default for now
                'rating': 4.5,  # Default rating
                'reviews': 100,  # Default review count
                'distance': '1.0 km',  # Default distance
                'image': restaurant.get('logo') or '/placeholder.svg?height=200&width=300',
                'deliveryTime': '25-35 min',  # Default delivery time
                'featured': True,
                'address': restaurant.get('address'),
                'description': restaurant.get('description'),
                'phone': restaurant.get('restaurant_phone'),
            })

        return Response(featured_restaurants, status=status.HTTP_200_OK)

