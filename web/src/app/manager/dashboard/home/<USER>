"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  MenuIcon as Restaurant,
  Users,
  ChefHat,
  TrendingUp,
  Plus,
} from "lucide-react";
import Link from "next/link";

export default function HomePage() {
  const stats = [
    {
      title: "Total Restaurants",
      value: "12",
      description: "Active locations",
      icon: Restaurant,
      color: "text-blue-600",
    },
    {
      title: "Total Staff",
      value: "156",
      description: "Across all locations",
      icon: Users,
      color: "text-green-600",
    },
    {
      title: "Menu Items",
      value: "342",
      description: "Total dishes available",
      icon: ChefHat,
      color: "text-orange-600",
    },
    {
      title: "Monthly Revenue",
      value: "MWK45,230",
      description: "This month's earnings",
      icon: TrendingUp,
      color: "text-purple-600",
    },
  ];

  const recentActivity = [
    {
      action: "New dish added",
      restaurant: "Downtown Branch",
      time: "2 hours ago",
    },
    {
      action: "Staff member hired",
      restaurant: "Mall Location",
      time: "4 hours ago",
    },
    { action: "Menu updated", restaurant: "Airport Branch", time: "1 day ago" },
    {
      action: "New restaurant added",
      restaurant: "Seaside Location",
      time: "2 days ago",
    },
  ];

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Restaurant Manager Dashboard
          </h1>
          <p className="text-gray-600">
            Welcome back! Here's what's happening with your restaurants.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </CardTitle>
                  <IconComponent className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Manage your restaurants efficiently
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link href="/manager/dashboard/restaurants">
                <Button
                  className="w-full h-20 flex flex-col gap-2"
                  variant="outline"
                >
                  <Restaurant className="h-6 w-6" />
                  View All Restaurants
                </Button>
              </Link>
              <Link href="/manager/dashboard/restaurants/create">
                <Button
                  className="w-full h-20 flex flex-col gap-2"
                  variant="outline"
                >
                  <Plus className="h-6 w-6" />
                  Add New Restaurant
                </Button>
              </Link>
              <Button
                className="w-full h-20 flex flex-col gap-2"
                variant="outline"
              >
                <Users className="h-6 w-6" />
                Manage Staff
              </Button>
              <Button
                className="w-full h-20 flex flex-col gap-2"
                variant="outline"
              >
                <ChefHat className="h-6 w-6" />
                Update Menus
              </Button>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest updates across your restaurants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex flex-col space-y-1">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.restaurant}
                    </p>
                    <p className="text-xs text-gray-400">{activity.time}</p>
                    {index < recentActivity.length - 1 && (
                      <hr className="mt-3" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>Key metrics for this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">+15%</div>
                <p className="text-sm text-gray-600">Revenue Growth</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">4.8/5</div>
                <p className="text-sm text-gray-600">Average Rating</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">23 min</div>
                <p className="text-sm text-gray-600">Avg Delivery Time</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
