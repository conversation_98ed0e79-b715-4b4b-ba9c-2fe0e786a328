"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Star,
  MapPin,
  Search,
  Clock,
  TrendingUp,
  Users,
  ChefHat,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import CTASection from "@/components/cta";

export default function LandingPage() {
  const [searchQuery, setSearchQuery] = useState("");

  const featuredRestaurants = [
    {
      id: 1,
      name: "Mzuzu Grill",
      cuisine: "Malawian",
      rating: 4.8,
      reviews: 324,
      distance: "0.5 km",
      image: "/placeholder.svg?height=200&width=300",
      deliveryTime: "25-35 min",
      featured: true,
    },
    {
      id: 2,
      name: "Nsima & More",
      cuisine: "Traditional Malawian",
      rating: 4.6,
      reviews: 189,
      distance: "1.2 km",
      image: "/placeholder.svg?height=200&width=300",
      deliveryTime: "30-40 min",
      featured: true,
    },
    {
      id: 3,
      name: "Lake Malawi Fish Spot",
      cuisine: "Seafood",
      rating: 4.9,
      reviews: 456,
      distance: "0.8 km",
      image: "/placeholder.svg?height=200&width=300",
      deliveryTime: "20-30 min",
      featured: true,
    },
  ];

  const categories = [
    { name: "Fast Food", icon: "🍔", count: 45 },
    { name: "Italian", icon: "🍝", count: 23 },
    { name: "Asian", icon: "🍜", count: 34 },
    { name: "Mexican", icon: "🌮", count: 18 },
    { name: "Desserts", icon: "🍰", count: 12 },
    { name: "Healthy", icon: "🥗", count: 28 },
  ];

  const stats = [
    { icon: Users, label: "Happy Customers", value: "50K+" },
    { icon: ChefHat, label: "Partner Restaurants", value: "200+" },
    { icon: TrendingUp, label: "Orders Delivered", value: "1M+" },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-300 to-red-200">
      {/* Header */}
      <header className="bg--transparent shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2 rounded-full p-2 bg-white/50">
              <Image width={50} height={50} src={"/logo1.png"} />
            </div>
            <nav className="hidden md:flex items-center gap-6">
              <Link
                href="/customer/restaurants"
                className="text-gray-500 hover:text-orange-600"
              >
                Restaurants
              </Link>

              <Link
                className="border border-2 border-orange-600 rounded-md bg-orange-600 hover:bg-orange-400 px-3 py-1 text-white"
                href={"/auth/login"}
              >
                Login
              </Link>
              <Link
                className="border border-2 border-orange-600 hover:bg-orange-200 px-3 py-1 rounded-md"
                href={"/auth/register"}
              >
                Register
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            Discover Amazing Food
            <span className="text-orange-600"> Near You</span>
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Find the best restaurants, read reviews, and get directions to your
            favorite spots
          </p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Search for restaurants, cuisines, or dishes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg rounded-full border-2 border-orange-200 focus:border-orange-400"
              />
              <Button className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full">
                Search
              </Button>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/customer/restaurants">
              <Button size="lg" className="rounded-full">
                <MapPin className="h-5 w-5 mr-2" />
                Browse All Restaurants
              </Button>
            </Link>
            <Link href="/customer/restaurants?nearby=true">
              <Button size="lg" variant="outline" className="rounded-full">
                Find Nearby
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Browse by Category
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category, index) => (
              <Card
                key={index}
                className="hover:shadow-lg transition-shadow cursor-pointer"
              >
                <CardContent className="p-6 text-center">
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {category.name}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {category.count} restaurants
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Restaurants */}
      <section className="py-16 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900">
              Featured Restaurants
            </h3>
            <Link href="/customer/restaurants">
              <Button variant="outline">View All</Button>
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredRestaurants.map((restaurant) => (
              <Card
                key={restaurant.id}
                className="overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="relative">
                  <img
                    src={restaurant.image || "/placeholder.svg"}
                    alt={restaurant.name}
                    className="w-full h-48 object-cover"
                  />
                  {restaurant.featured && (
                    <Badge className="absolute top-3 left-3 bg-orange-600">
                      Featured
                    </Badge>
                  )}
                </div>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-xl font-semibold text-gray-900">
                      {restaurant.name}
                    </h4>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">
                        {restaurant.rating}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-3">{restaurant.cuisine}</p>
                  <div className="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {restaurant.distance}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {restaurant.deliveryTime}
                    </div>
                  </div>
                  <Link href={`/restaurants/${restaurant.id}`}>
                    <Button className="w-full">View Restaurant</Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-6 bg-orange-600 text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index}>
                  <IconComponent className="h-12 w-12 mx-auto mb-4" />
                  <div className="text-3xl font-bold mb-2">{stat.value}</div>
                  <div className="text-orange-100">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      <CTASection />

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-6">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Image
                  alt="EasyEats"
                  width={70}
                  height={70}
                  src={"/logo1.png"}
                />
              </div>
              <p className="text-gray-400">
                Discover the best restaurants and food experiences in your city.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/customer/restaurants">Restaurants</Link>
                </li>
                <li>
                  <Link href="/customer/history">Order History</Link>
                </li>
                <li>
                  <Link href="/about">About Us</Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/help">Help Center</Link>
                </li>
                <li>
                  <Link href="/contact">Contact Us</Link>
                </li>
                <li>
                  <Link href="/terms">Terms of Service</Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <p className="text-gray-400 mb-2">Follow us for updates</p>
              <div className="flex gap-4">
                <Button variant="outline" size="sm">
                  Facebook
                </Button>
                <Button variant="outline" size="sm">
                  Twitter
                </Button>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 EasyEats. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
